package com.crypto.trading.market.monitor;

import com.crypto.trading.market.manager.WebSocketManager;
import com.crypto.trading.market.config.MarketDataConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * WebSocket连接监控器
 * 监控WebSocket连接状态，及时发现和修复连接问题
 */
@Component
public class WebSocketConnectionMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketConnectionMonitor.class);
    
    @Autowired
    private WebSocketManager webSocketManager;
    
    @Autowired
    private MarketDataConfig marketDataConfig;
    
    /**
     * 监控执行器
     */
    private ScheduledExecutorService monitorExecutor;
    
    /**
     * 监控统计
     */
    private final AtomicLong totalChecks = new AtomicLong(0);
    private final AtomicLong connectionFailures = new AtomicLong(0);
    private final AtomicLong autoRepairs = new AtomicLong(0);
    private final AtomicBoolean isMonitoring = new AtomicBoolean(false);
    
    /**
     * 上次数据接收时间
     */
    private volatile LocalDateTime lastDataReceived = LocalDateTime.now();
    
    /**
     * 数据接收计数器
     */
    private final AtomicLong dataReceiveCount = new AtomicLong(0);
    
    /**
     * 最大无数据时间（分钟）
     */
    private static final int MAX_NO_DATA_MINUTES = 5;
    
    @PostConstruct
    public void init() {
        log.info("初始化WebSocket连接监控器...");
        
        // 创建监控执行器
        monitorExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "websocket-connection-monitor");
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动监控任务
        startMonitoring();
        
        log.info("WebSocket连接监控器已启动");
    }
    
    @PreDestroy
    public void destroy() {
        log.info("关闭WebSocket连接监控器...");
        
        isMonitoring.set(false);
        
        if (monitorExecutor != null) {
            monitorExecutor.shutdown();
            try {
                if (!monitorExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 生成最终监控报告
        generateFinalReport();
        
        log.info("WebSocket连接监控器已关闭");
    }
    
    /**
     * 启动监控任务
     */
    private void startMonitoring() {
        isMonitoring.set(true);
        
        // 每30秒检查一次连接状态
        monitorExecutor.scheduleAtFixedRate(
                this::checkConnectionStatus,
                30, 30, TimeUnit.SECONDS);
        
        // 每2分钟检查一次数据流
        monitorExecutor.scheduleAtFixedRate(
                this::checkDataFlow,
                120, 120, TimeUnit.SECONDS);
        
        // 每10分钟生成一次监控报告
        monitorExecutor.scheduleAtFixedRate(
                this::generateMonitoringReport,
                600, 600, TimeUnit.SECONDS);
    }
    
    /**
     * 检查连接状态
     */
    private void checkConnectionStatus() {
        if (!isMonitoring.get()) {
            return;
        }
        
        try {
            totalChecks.incrementAndGet();
            
            boolean isRunning = webSocketManager.isRunning();
            
            if (!isRunning) {
                log.warn("检测到WebSocket管理器未运行，尝试重启...");
                connectionFailures.incrementAndGet();
                
                // 尝试自动修复
                attemptAutoRepair();
            } else {
                log.debug("WebSocket连接状态检查正常");
            }
            
        } catch (Exception e) {
            log.error("检查WebSocket连接状态时发生异常: {}", e.getMessage(), e);
            connectionFailures.incrementAndGet();
        }
    }
    
    /**
     * 检查数据流
     */
    private void checkDataFlow() {
        if (!isMonitoring.get()) {
            return;
        }
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime threshold = now.minusMinutes(MAX_NO_DATA_MINUTES);
            
            if (lastDataReceived.isBefore(threshold)) {
                log.warn("检测到数据流中断，上次接收数据时间: {}, 当前时间: {}", 
                        lastDataReceived.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                        now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                
                connectionFailures.incrementAndGet();
                
                // 尝试重启WebSocket连接
                log.info("尝试重启WebSocket连接以恢复数据流...");
                attemptAutoRepair();
            } else {
                log.debug("数据流检查正常，上次接收数据时间: {}", 
                        lastDataReceived.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            
        } catch (Exception e) {
            log.error("检查数据流时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 尝试自动修复
     */
    private void attemptAutoRepair() {
        try {
            log.info("开始自动修复WebSocket连接...");
            
            // 重启WebSocket监听器
            webSocketManager.restartListeners();
            
            // 等待一段时间让连接稳定
            Thread.sleep(5000);
            
            // 检查修复结果
            if (webSocketManager.isRunning()) {
                log.info("WebSocket连接自动修复成功");
                autoRepairs.incrementAndGet();
                
                // 重置数据接收时间
                lastDataReceived = LocalDateTime.now();
            } else {
                log.error("WebSocket连接自动修复失败");
            }
            
        } catch (Exception e) {
            log.error("自动修复WebSocket连接时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 生成监控报告
     */
    private void generateMonitoringReport() {
        try {
            log.info("=== WebSocket连接监控报告 ===");
            log.info("报告时间: {}", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            log.info("总检查次数: {}", totalChecks.get());
            log.info("连接失败次数: {}", connectionFailures.get());
            log.info("自动修复次数: {}", autoRepairs.get());
            log.info("数据接收计数: {}", dataReceiveCount.get());
            log.info("上次数据接收时间: {}", lastDataReceived.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            log.info("WebSocket管理器运行状态: {}", webSocketManager.isRunning());
            
            // 计算健康度评分
            double healthScore = calculateHealthScore();
            log.info("连接健康度评分: {:.2f}/100", healthScore);
            
            if (healthScore < 70.0) {
                log.error("WebSocket连接健康度较低，建议检查网络和配置");
            } else if (healthScore < 85.0) {
                log.warn("WebSocket连接健康度一般，建议关注连接稳定性");
            }
            
            log.info("===============================");
            
        } catch (Exception e) {
            log.error("生成WebSocket监控报告时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 生成最终报告
     */
    private void generateFinalReport() {
        try {
            log.info("=== WebSocket连接监控最终报告 ===");
            log.info("监控结束时间: {}", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            log.info("总检查次数: {}", totalChecks.get());
            log.info("总连接失败次数: {}", connectionFailures.get());
            log.info("总自动修复次数: {}", autoRepairs.get());
            log.info("总数据接收计数: {}", dataReceiveCount.get());
            
            double healthScore = calculateHealthScore();
            log.info("最终健康度评分: {:.2f}/100", healthScore);
            
            // 计算可用性
            double availability = totalChecks.get() > 0 ? 
                    100.0 * (totalChecks.get() - connectionFailures.get()) / totalChecks.get() : 100.0;
            log.info("连接可用性: {:.2f}%", availability);
            
            log.info("==================================");
            
        } catch (Exception e) {
            log.error("生成WebSocket最终报告时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 计算健康度评分
     */
    private double calculateHealthScore() {
        long totalChecks = this.totalChecks.get();
        long failures = connectionFailures.get();
        long repairs = autoRepairs.get();
        
        if (totalChecks == 0) {
            return 100.0;
        }
        
        // 基础分数
        double score = 100.0;
        
        // 失败率扣分
        double failureRate = (double) failures / totalChecks;
        score -= failureRate * 50; // 失败率每10%扣5分
        
        // 自动修复加分（说明系统有自愈能力）
        if (failures > 0) {
            double repairRate = (double) repairs / failures;
            score += repairRate * 10; // 修复率每10%加1分
        }
        
        // 数据流检查
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.minusMinutes(MAX_NO_DATA_MINUTES);
        if (lastDataReceived.isBefore(threshold)) {
            score -= 20; // 数据流中断扣20分
        }
        
        return Math.max(score, 0.0);
    }
    
    /**
     * 记录数据接收
     */
    public void recordDataReceived() {
        lastDataReceived = LocalDateTime.now();
        dataReceiveCount.incrementAndGet();
    }
    
    /**
     * 获取监控统计信息
     */
    public MonitoringStats getStats() {
        return new MonitoringStats(
                totalChecks.get(),
                connectionFailures.get(),
                autoRepairs.get(),
                dataReceiveCount.get(),
                lastDataReceived,
                calculateHealthScore()
        );
    }
    
    /**
     * 监控统计信息
     */
    public static class MonitoringStats {
        private final long totalChecks;
        private final long connectionFailures;
        private final long autoRepairs;
        private final long dataReceiveCount;
        private final LocalDateTime lastDataReceived;
        private final double healthScore;
        
        public MonitoringStats(long totalChecks, long connectionFailures, long autoRepairs,
                             long dataReceiveCount, LocalDateTime lastDataReceived, double healthScore) {
            this.totalChecks = totalChecks;
            this.connectionFailures = connectionFailures;
            this.autoRepairs = autoRepairs;
            this.dataReceiveCount = dataReceiveCount;
            this.lastDataReceived = lastDataReceived;
            this.healthScore = healthScore;
        }
        
        // Getters
        public long getTotalChecks() { return totalChecks; }
        public long getConnectionFailures() { return connectionFailures; }
        public long getAutoRepairs() { return autoRepairs; }
        public long getDataReceiveCount() { return dataReceiveCount; }
        public LocalDateTime getLastDataReceived() { return lastDataReceived; }
        public double getHealthScore() { return healthScore; }
        
        @Override
        public String toString() {
            return String.format("MonitoringStats{checks=%d, failures=%d, repairs=%d, dataCount=%d, lastData=%s, health=%.2f}", 
                    totalChecks, connectionFailures, autoRepairs, dataReceiveCount, 
                    lastDataReceived.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), healthScore);
        }
    }
}
