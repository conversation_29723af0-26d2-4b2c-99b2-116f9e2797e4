package com.crypto.trading.market.health;

import com.crypto.trading.market.manager.WebSocketManager;
import com.crypto.trading.market.monitor.WebSocketConnectionMonitor;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.common.util.DatabaseExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据流健康检查器
 * 全面监控数据流的健康状态，及时发现和修复问题
 */
@Component
public class DataFlowHealthChecker {
    
    private static final Logger log = LoggerFactory.getLogger(DataFlowHealthChecker.class);
    
    @Autowired
    private WebSocketManager webSocketManager;
    
    @Autowired
    private WebSocketConnectionMonitor connectionMonitor;
    
    @Autowired
    private InfluxDBRepository influxDBRepository;
    
    /**
     * 健康检查执行器
     */
    private ScheduledExecutorService healthCheckExecutor;
    
    /**
     * 健康检查统计
     */
    private final AtomicLong totalHealthChecks = new AtomicLong(0);
    private final AtomicLong healthCheckFailures = new AtomicLong(0);
    private final AtomicLong autoRepairAttempts = new AtomicLong(0);
    private final AtomicLong successfulRepairs = new AtomicLong(0);
    private final AtomicBoolean isHealthy = new AtomicBoolean(true);
    
    /**
     * 上次健康检查时间
     */
    private volatile LocalDateTime lastHealthCheckTime = LocalDateTime.now();
    
    /**
     * 健康检查间隔（秒）
     */
    private static final int HEALTH_CHECK_INTERVAL_SECONDS = 60;
    
    /**
     * 深度健康检查间隔（分钟）
     */
    private static final int DEEP_HEALTH_CHECK_INTERVAL_MINUTES = 10;
    
    @PostConstruct
    public void init() {
        log.info("初始化数据流健康检查器...");
        
        // 创建健康检查执行器
        healthCheckExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "data-flow-health-checker");
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动健康检查任务
        startHealthChecks();
        
        log.info("数据流健康检查器已启动");
    }
    
    @PreDestroy
    public void destroy() {
        log.info("关闭数据流健康检查器...");
        
        if (healthCheckExecutor != null) {
            healthCheckExecutor.shutdown();
            try {
                if (!healthCheckExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    healthCheckExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                healthCheckExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 生成最终健康报告
        generateFinalHealthReport();
        
        log.info("数据流健康检查器已关闭");
    }
    
    /**
     * 启动健康检查任务
     */
    private void startHealthChecks() {
        // 基础健康检查 - 每分钟执行一次
        healthCheckExecutor.scheduleAtFixedRate(
                this::performBasicHealthCheck,
                HEALTH_CHECK_INTERVAL_SECONDS,
                HEALTH_CHECK_INTERVAL_SECONDS,
                TimeUnit.SECONDS);
        
        // 深度健康检查 - 每10分钟执行一次
        healthCheckExecutor.scheduleAtFixedRate(
                this::performDeepHealthCheck,
                DEEP_HEALTH_CHECK_INTERVAL_MINUTES,
                DEEP_HEALTH_CHECK_INTERVAL_MINUTES,
                TimeUnit.MINUTES);
    }
    
    /**
     * 执行基础健康检查
     */
    private void performBasicHealthCheck() {
        try {
            totalHealthChecks.incrementAndGet();
            lastHealthCheckTime = LocalDateTime.now();
            
            log.debug("执行基础健康检查...");
            
            boolean overallHealth = true;
            StringBuilder healthReport = new StringBuilder();
            healthReport.append("基础健康检查报告:\n");
            
            // 1. 检查WebSocket管理器状态
            boolean webSocketHealth = checkWebSocketManagerHealth();
            healthReport.append(String.format("- WebSocket管理器: %s\n", webSocketHealth ? "健康" : "异常"));
            overallHealth &= webSocketHealth;
            
            // 2. 检查连接监控器状态
            boolean connectionHealth = checkConnectionMonitorHealth();
            healthReport.append(String.format("- 连接监控器: %s\n", connectionHealth ? "健康" : "异常"));
            overallHealth &= connectionHealth;
            
            // 3. 检查数据库执行器状态
            boolean databaseHealth = checkDatabaseExecutorHealth();
            healthReport.append(String.format("- 数据库执行器: %s\n", databaseHealth ? "健康" : "异常"));
            overallHealth &= databaseHealth;
            
            // 更新整体健康状态
            isHealthy.set(overallHealth);
            
            if (!overallHealth) {
                log.warn("基础健康检查发现问题:\n{}", healthReport.toString());
                healthCheckFailures.incrementAndGet();
                
                // 尝试自动修复
                attemptAutoRepair();
            } else {
                log.debug("基础健康检查通过");
            }
            
        } catch (Exception e) {
            log.error("执行基础健康检查时发生异常: {}", e.getMessage(), e);
            healthCheckFailures.incrementAndGet();
            isHealthy.set(false);
        }
    }
    
    /**
     * 执行深度健康检查
     */
    private void performDeepHealthCheck() {
        try {
            log.info("执行深度健康检查...");
            
            StringBuilder deepReport = new StringBuilder();
            deepReport.append("深度健康检查报告:\n");
            
            // 1. 检查InfluxDB连接和性能
            boolean influxDBHealth = checkInfluxDBHealth();
            deepReport.append(String.format("- InfluxDB连接: %s\n", influxDBHealth ? "健康" : "异常"));
            
            // 2. 检查虚拟线程执行器状态
            boolean virtualThreadHealth = checkVirtualThreadHealth();
            deepReport.append(String.format("- 虚拟线程执行器: %s\n", virtualThreadHealth ? "健康" : "异常"));
            
            // 3. 检查数据流量和延迟
            boolean dataFlowHealth = checkDataFlowHealth();
            deepReport.append(String.format("- 数据流: %s\n", dataFlowHealth ? "健康" : "异常"));
            
            // 4. 生成性能统计
            generatePerformanceStats(deepReport);
            
            log.info("深度健康检查完成:\n{}", deepReport.toString());
            
        } catch (Exception e) {
            log.error("执行深度健康检查时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查WebSocket管理器健康状态
     */
    private boolean checkWebSocketManagerHealth() {
        try {
            return webSocketManager.isRunning();
        } catch (Exception e) {
            log.error("检查WebSocket管理器健康状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查连接监控器健康状态
     */
    private boolean checkConnectionMonitorHealth() {
        try {
            var stats = connectionMonitor.getStats();
            return stats.getHealthScore() > 70.0; // 健康分数阈值
        } catch (Exception e) {
            log.error("检查连接监控器健康状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查数据库执行器健康状态
     */
    private boolean checkDatabaseExecutorHealth() {
        try {
            // 检查是否正在关闭
            if (DatabaseExecutor.isShuttingDown()) {
                return false;
            }
            
            // 检查活跃事务数量
            long activeTransactions = DatabaseExecutor.getActiveTransactionCount();
            return activeTransactions < 50; // 活跃事务数量阈值
        } catch (Exception e) {
            log.error("检查数据库执行器健康状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查InfluxDB健康状态
     */
    private boolean checkInfluxDBHealth() {
        try {
            // 这里可以添加InfluxDB连接测试
            // 例如执行一个简单的查询来验证连接
            return true; // 简化实现
        } catch (Exception e) {
            log.error("检查InfluxDB健康状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查虚拟线程健康状态
     */
    private boolean checkVirtualThreadHealth() {
        try {
            var dbStats = DatabaseExecutor.getStatistics();
            double successRate = (Double) dbStats.get("successRate");
            return successRate > 95.0; // 成功率阈值
        } catch (Exception e) {
            log.error("检查虚拟线程健康状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查数据流健康状态
     */
    private boolean checkDataFlowHealth() {
        try {
            var connectionStats = connectionMonitor.getStats();
            LocalDateTime lastDataReceived = connectionStats.getLastDataReceived();
            LocalDateTime threshold = LocalDateTime.now().minusMinutes(5);
            
            return lastDataReceived.isAfter(threshold);
        } catch (Exception e) {
            log.error("检查数据流健康状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 生成性能统计
     */
    private void generatePerformanceStats(StringBuilder report) {
        try {
            var dbStats = DatabaseExecutor.getStatistics();
            var connectionStats = connectionMonitor.getStats();
            
            report.append("性能统计:\n");
            report.append(String.format("- 数据库操作成功率: %.2f%%\n", (Double) dbStats.get("successRate")));
            report.append(String.format("- 连接健康度评分: %.2f/100\n", connectionStats.getHealthScore()));
            report.append(String.format("- 总数据接收量: %d\n", connectionStats.getDataReceiveCount()));
            report.append(String.format("- 连接失败次数: %d\n", connectionStats.getConnectionFailures()));
            report.append(String.format("- 自动修复次数: %d\n", connectionStats.getAutoRepairs()));
            
        } catch (Exception e) {
            report.append("- 性能统计获取失败: ").append(e.getMessage()).append("\n");
        }
    }
    
    /**
     * 尝试自动修复
     */
    private void attemptAutoRepair() {
        try {
            log.info("开始自动修复数据流问题...");
            autoRepairAttempts.incrementAndGet();
            
            boolean repairSuccess = false;
            
            // 1. 尝试重启WebSocket连接
            if (!webSocketManager.isRunning()) {
                log.info("尝试重启WebSocket管理器...");
                webSocketManager.restartListeners();
                Thread.sleep(5000); // 等待重启完成
                
                if (webSocketManager.isRunning()) {
                    log.info("WebSocket管理器重启成功");
                    repairSuccess = true;
                }
            }
            
            if (repairSuccess) {
                successfulRepairs.incrementAndGet();
                log.info("自动修复成功");
            } else {
                log.warn("自动修复失败，需要人工干预");
            }
            
        } catch (Exception e) {
            log.error("自动修复过程中发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 生成最终健康报告
     */
    private void generateFinalHealthReport() {
        try {
            log.info("=== 数据流健康检查最终报告 ===");
            log.info("检查结束时间: {}", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            log.info("总健康检查次数: {}", totalHealthChecks.get());
            log.info("健康检查失败次数: {}", healthCheckFailures.get());
            log.info("自动修复尝试次数: {}", autoRepairAttempts.get());
            log.info("成功修复次数: {}", successfulRepairs.get());
            log.info("最终健康状态: {}", isHealthy.get() ? "健康" : "异常");
            
            // 计算可用性
            double availability = totalHealthChecks.get() > 0 ? 
                    100.0 * (totalHealthChecks.get() - healthCheckFailures.get()) / totalHealthChecks.get() : 100.0;
            log.info("系统可用性: {:.2f}%", availability);
            
            // 计算修复成功率
            double repairSuccessRate = autoRepairAttempts.get() > 0 ? 
                    100.0 * successfulRepairs.get() / autoRepairAttempts.get() : 0.0;
            log.info("自动修复成功率: {:.2f}%", repairSuccessRate);
            
            log.info("=====================================");
            
        } catch (Exception e) {
            log.error("生成最终健康报告时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取健康状态
     */
    public boolean isHealthy() {
        return isHealthy.get();
    }
    
    /**
     * 获取健康检查统计信息
     */
    public HealthCheckStats getStats() {
        return new HealthCheckStats(
                totalHealthChecks.get(),
                healthCheckFailures.get(),
                autoRepairAttempts.get(),
                successfulRepairs.get(),
                isHealthy.get(),
                lastHealthCheckTime
        );
    }
    
    /**
     * 健康检查统计信息
     */
    public static class HealthCheckStats {
        private final long totalChecks;
        private final long failures;
        private final long repairAttempts;
        private final long successfulRepairs;
        private final boolean isHealthy;
        private final LocalDateTime lastCheckTime;
        
        public HealthCheckStats(long totalChecks, long failures, long repairAttempts,
                              long successfulRepairs, boolean isHealthy, LocalDateTime lastCheckTime) {
            this.totalChecks = totalChecks;
            this.failures = failures;
            this.repairAttempts = repairAttempts;
            this.successfulRepairs = successfulRepairs;
            this.isHealthy = isHealthy;
            this.lastCheckTime = lastCheckTime;
        }
        
        // Getters
        public long getTotalChecks() { return totalChecks; }
        public long getFailures() { return failures; }
        public long getRepairAttempts() { return repairAttempts; }
        public long getSuccessfulRepairs() { return successfulRepairs; }
        public boolean isHealthy() { return isHealthy; }
        public LocalDateTime getLastCheckTime() { return lastCheckTime; }
        
        @Override
        public String toString() {
            return String.format("HealthCheckStats{checks=%d, failures=%d, repairs=%d/%d, healthy=%s, lastCheck=%s}", 
                    totalChecks, failures, successfulRepairs, repairAttempts, isHealthy, 
                    lastCheckTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
    }
}
