-------------------------------------------------------------------------------
Test set: com.crypto.trading.market.processor.KlineDataProcessorTest
-------------------------------------------------------------------------------
Tests run: 3, Failures: 0, Errors: 3, Skipped: 0, Time elapsed: 0.021 s <<< FAILURE! -- in com.crypto.trading.market.processor.KlineDataProcessorTest
com.crypto.trading.market.processor.KlineDataProcessorTest.testProcessKlineDataWithKafkaException -- Time elapsed: 0.005 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.crypto.trading.market.monitor.WebSocketConnectionMonitor.recordDataReceived()" because "this.connectionMonitor" is null
	at com.crypto.trading.market.processor.KlineDataProcessor.processKlineData(KlineDataProcessor.java:76)
	at com.crypto.trading.market.processor.KlineDataProcessorTest.testProcessKlineDataWithKafkaException(KlineDataProcessorTest.java:122)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.crypto.trading.market.processor.KlineDataProcessorTest.testProcessKlineData -- Time elapsed: 0.011 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.crypto.trading.market.monitor.WebSocketConnectionMonitor.recordDataReceived()" because "this.connectionMonitor" is null
	at com.crypto.trading.market.processor.KlineDataProcessor.processKlineData(KlineDataProcessor.java:76)
	at com.crypto.trading.market.processor.KlineDataProcessorTest.testProcessKlineData(KlineDataProcessorTest.java:107)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.crypto.trading.market.processor.KlineDataProcessorTest.testProcessKlineDataWithInfluxDBException -- Time elapsed: 0.004 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.crypto.trading.market.monitor.WebSocketConnectionMonitor.recordDataReceived()" because "this.connectionMonitor" is null
	at com.crypto.trading.market.processor.KlineDataProcessor.processKlineData(KlineDataProcessor.java:76)
	at com.crypto.trading.market.processor.KlineDataProcessorTest.testProcessKlineDataWithInfluxDBException(KlineDataProcessorTest.java:134)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

