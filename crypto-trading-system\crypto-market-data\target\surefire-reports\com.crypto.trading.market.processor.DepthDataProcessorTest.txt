-------------------------------------------------------------------------------
Test set: com.crypto.trading.market.processor.DepthDataProcessorTest
-------------------------------------------------------------------------------
Tests run: 4, Failures: 0, Errors: 4, Skipped: 0, Time elapsed: 0.381 s <<< FAILURE! -- in com.crypto.trading.market.processor.DepthDataProcessorTest
com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthData -- Time elapsed: 0.365 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.crypto.trading.market.monitor.WebSocketConnectionMonitor.recordDataReceived()" because "this.connectionMonitor" is null
	at com.crypto.trading.market.processor.DepthDataProcessor.processDepthData(DepthDataProcessor.java:78)
	at com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthData(DepthDataProcessorTest.java:100)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthDataWithInfluxDBException -- Time elapsed: 0.004 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.crypto.trading.market.monitor.WebSocketConnectionMonitor.recordDataReceived()" because "this.connectionMonitor" is null
	at com.crypto.trading.market.processor.DepthDataProcessor.processDepthData(DepthDataProcessor.java:78)
	at com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthDataWithInfluxDBException(DepthDataProcessorTest.java:127)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthDataWithKafkaException -- Time elapsed: 0.003 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.crypto.trading.market.monitor.WebSocketConnectionMonitor.recordDataReceived()" because "this.connectionMonitor" is null
	at com.crypto.trading.market.processor.DepthDataProcessor.processDepthData(DepthDataProcessor.java:78)
	at com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthDataWithKafkaException(DepthDataProcessorTest.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthDataWithJsonException -- Time elapsed: 0.003 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.crypto.trading.market.monitor.WebSocketConnectionMonitor.recordDataReceived()" because "this.connectionMonitor" is null
	at com.crypto.trading.market.processor.DepthDataProcessor.processDepthData(DepthDataProcessor.java:78)
	at com.crypto.trading.market.processor.DepthDataProcessorTest.testProcessDepthDataWithJsonException(DepthDataProcessorTest.java:139)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

