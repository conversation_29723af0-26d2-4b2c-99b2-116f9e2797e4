package com.crypto.trading.market.config;

import com.crypto.trading.common.config.AbstractKafkaProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * Kafka生产者配置类
 * 负责提供Kafka生产者相关的配置和参数
 */
@Configuration
public class KafkaProducerConfig extends AbstractKafkaProducerConfig {

    /**
     * K线数据主题
     */
    @Value("${market.kline.topic:kline.data}")
    private String klineTopic;

    /**
     * 深度数据主题
     */
    @Value("${market.depth.topic:depth.data}")
    private String depthTopic;

    /**
     * 交易数据主题
     */
    @Value("${market.trade.topic:trade.data}")
    private String tradeTopic;

    /**
     * 配置字符串序列化的Kafka生产者工厂
     *
     * @return 字符串序列化的Kafka生产者工厂
     */
    @Bean
    public ProducerFactory<String, String> producerFactory() {
        return createStringProducerFactory();
    }

    /**
     * 配置字符串序列化的Kafka模板
     *
     * @return 字符串序列化的Kafka模板
     */
    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        KafkaTemplate<String, String> template = createKafkaTemplate(producerFactory(), null);
        
        // 设置默认的主题分区数和副本因子
        template.setProducerListener(new CustomProducerListener(
                kafkaConfig.getDefaultPartitions(), 
                kafkaConfig.getDefaultReplicationFactor()
        ));
        
        return template;
    }
    
    /**
     * 配置JSON序列化的Kafka生产者工厂
     *
     * @return JSON序列化的Kafka生产者工厂
     */
    @Bean
    public ProducerFactory<String, Object> jsonProducerFactory() {
        return createJsonProducerFactory();
    }

    /**
     * 配置JSON序列化的Kafka模板
     *
     * @return JSON序列化的Kafka模板
     */
    @Bean
    public KafkaTemplate<String, Object> jsonKafkaTemplate() {
        return createKafkaTemplate(jsonProducerFactory(), null);
    }

    /**
     * 配置Avro序列化的Kafka生产者工厂
     *
     * @return Avro序列化的Kafka生产者工厂
     */
    @Bean
    public ProducerFactory<String, Object> avroProducerFactory() {
        return createAvroProducerFactory();
    }

    /**
     * 配置Avro序列化的Kafka模板
     *
     * @return Avro序列化的Kafka模板
     */
    @Bean
    public KafkaTemplate<String, Object> avroKafkaTemplate() {
        return createKafkaTemplate(avroProducerFactory(), null);
    }
    
    /**
     * 获取K线数据主题
     *
     * @return K线数据主题
     */
    public String getKlineTopic() {
        return klineTopic;
    }
    
    /**
     * 获取深度数据主题
     *
     * @return 深度数据主题
     */
    public String getDepthTopic() {
        return depthTopic;
    }
    
    /**
     * 获取交易数据主题
     *
     * @return 交易数据主题
     */
    public String getTradeTopic() {
        return tradeTopic;
    }
    
    /**
     * 获取模块名称，用于构建事务ID
     *
     * @return 模块名称
     */
    @Override
    protected String getModuleName() {
        return "market-data";
    }
    
    /**
     * Kafka生产者监听器
     * 用于监听生产者事件
     */
    private static class CustomProducerListener implements org.springframework.kafka.support.ProducerListener<String, String> {
        private final int defaultPartitions;
        private final short defaultReplicationFactor;

        public CustomProducerListener(int defaultPartitions, short defaultReplicationFactor) {
            this.defaultPartitions = defaultPartitions;
            this.defaultReplicationFactor = defaultReplicationFactor;
        }

        @Override
        public void onSuccess(ProducerRecord<String, String> producerRecord, RecordMetadata recordMetadata) {
            // 可以添加成功发送消息的处理逻辑
        }

        @Override
        public void onError(ProducerRecord<String, String> producerRecord, RecordMetadata recordMetadata, Exception exception) {
            // 可以添加发送消息失败的处理逻辑
        }
    }
}