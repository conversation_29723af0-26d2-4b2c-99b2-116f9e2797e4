package com.crypto.trading.market.manager;

import com.crypto.trading.common.util.VirtualThreadMonitor;
import com.crypto.trading.market.config.MarketDataConfig;
import com.crypto.trading.market.config.WebSocketConfig;
import com.crypto.trading.market.listener.DepthDataListener;
import com.crypto.trading.market.listener.KlineDataListener;
import com.crypto.trading.market.listener.TradeDataListener;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.websocket.WebSocketHealthChecker;
import com.crypto.trading.sdk.websocket.WebSocketConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

/**
 * WebSocket管理器
 * 负责协调各个WebSocket监听器的启动和停止
 */
@Component
public class WebSocketManager {

    private static final Logger log = LoggerFactory.getLogger(WebSocketManager.class);

    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private WebSocketConfig webSocketConfig;

    @Autowired
    private DepthDataListener depthDataListener;

    @Autowired
    private KlineDataListener klineDataListener;

    @Autowired
    private TradeDataListener tradeDataListener;

    @Autowired
    private InfluxDBRepository influxDBRepository;
    
    @Autowired
    private WebSocketConnectionPool connectionPool;
    
    @Autowired
    private WebSocketHealthChecker healthChecker;
    
    /**
     * 运行状态标志
     */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /**
     * 虚拟线程执行器
     * 用于异步执行WebSocket连接操作
     */
    private ExecutorService virtualThreadExecutor;
    
    /**
     * 虚拟线程监控器
     */
    private final VirtualThreadMonitor virtualThreadMonitor = VirtualThreadMonitor.getInstance();
    
    /**
     * 连接计数器
     */
    private final AtomicInteger connectionCounter = new AtomicInteger(0);
    
    /**
     * 连接映射表
     * 键为连接描述，值为连接ID
     */
    private final Map<String, Integer> connectionMap = new ConcurrentHashMap<>();

    /**
     * 初始化方法
     * 在应用启动时启动WebSocket监听
     */
    @PostConstruct
    public void init() {
        // 创建虚拟线程执行器
        this.virtualThreadExecutor = virtualThreadMonitor.createMonitoredVirtualThreadExecutor("websocket-manager");
        log.info("WebSocket管理器虚拟线程执行器已初始化");
        
        // 启动WebSocket监听器
        startListeners();
    }
    
    /**
     * 销毁方法
     * 在应用关闭时停止WebSocket监听
     */
    @PreDestroy
    public void destroy() {
        // 停止WebSocket监听器
        stopListeners();
        
        // 关闭虚拟线程执行器
        if (virtualThreadExecutor != null) {
            virtualThreadExecutor.shutdown();
            try {
                if (!virtualThreadExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    virtualThreadExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                virtualThreadExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("WebSocket管理器虚拟线程执行器已关闭");
        }
    }

    /**
     * 启动所有WebSocket监听器
     */
    public synchronized void startListeners() {
        if (running.get()) {
            log.info("WebSocket监听器已经在运行中");
            return;
        }
        
        log.info("开始启动所有WebSocket监听器...");
        
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 创建任务列表
            List<Supplier<Void>> tasks = new ArrayList<>();
            
            // 添加深度数据监听器任务
            if (marketDataConfig.isDepthEnabled()) {
                tasks.add(() -> {
                    depthDataListener.startDepthDataSubscription();
                    return null;
                });
            }
            
            // 添加K线数据监听器任务
            if (marketDataConfig.isKlineEnabled()) {
                tasks.add(() -> {
                    klineDataListener.startKlineDataSubscription();
                    return null;
                });
            }
            
            // 添加交易数据监听器任务
            if (marketDataConfig.isTradeEnabled()) {
                tasks.add(() -> {
                    tradeDataListener.startTradeDataSubscription();
                    return null;
                });
            }
            
            // 并行执行所有任务
            CompletableFuture<?>[] futures = tasks.stream()
                    .map(task -> CompletableFuture.supplyAsync(task, virtualThreadExecutor))
                    .toArray(CompletableFuture[]::new);
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures).join();
            
            // 设置运行状态
            running.set(true);
            
            // 记录完成时间
            long duration = System.currentTimeMillis() - startTime;
            log.info("所有WebSocket监听器已启动，耗时: {}毫秒", duration);
        } catch (Exception e) {
            log.error("启动WebSocket监听器异常", e);
            // 尝试停止已启动的监听器
            stopListeners();
            // 重新抛出异常
            throw new RuntimeException("启动WebSocket监听器失败", e);
        }
    }

    /**
     * 停止所有WebSocket监听器
     */
    public synchronized void stopListeners() {
        if (!running.get()) {
            log.info("WebSocket监听器已经停止");
            return;
        }
        
        log.info("开始停止所有WebSocket监听器...");
        
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 创建任务列表
            List<Runnable> tasks = new ArrayList<>();
            
            // 添加深度数据监听器停止任务
            tasks.add(() -> {
                try {
                    depthDataListener.stopDepthDataSubscription();
                } catch (Exception e) {
                    log.error("停止深度数据监听器异常", e);
                }
            });
            
            // 添加K线数据监听器停止任务
            tasks.add(() -> {
                try {
                    klineDataListener.stopKlineDataSubscription();
                } catch (Exception e) {
                    log.error("停止K线数据监听器异常", e);
                }
            });
            
            // 添加交易数据监听器停止任务
            tasks.add(() -> {
                try {
                    tradeDataListener.stopTradeDataSubscription();
                } catch (Exception e) {
                    log.error("停止交易数据监听器异常", e);
                }
            });
            
            // 并行执行所有任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (Runnable task : tasks) {
                futures.add(CompletableFuture.runAsync(task, virtualThreadExecutor));
            }
            
            // 等待所有任务完成，设置超时时间
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .orTimeout(webSocketConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                        .join();
            } catch (Exception e) {
                log.warn("停止WebSocket监听器超时: {}", e.getMessage());
            }
            
            // 设置运行状态
            running.set(false);
            
            // 记录完成时间
            long duration = System.currentTimeMillis() - startTime;
            log.info("所有WebSocket监听器已停止，耗时: {}毫秒", duration);
        } catch (Exception e) {
            log.error("停止WebSocket监听器异常", e);
            // 设置运行状态为false，即使发生异常
            running.set(false);
            // 重新抛出异常
            throw new RuntimeException("停止WebSocket监听器失败", e);
        }
    }
    
    /**
     * 重启所有WebSocket监听器
     */
    public synchronized void restartListeners() {
        log.info("重启所有WebSocket监听器...");
        
        // 使用统一的虚拟线程执行器异步执行重启操作
        virtualThreadExecutor.submit(() -> {
            try {
                // 记录开始时间
                long startTime = System.currentTimeMillis();
                
                // 停止监听器
                stopListeners();
                
                // 等待一段时间，确保所有连接都已关闭
                Thread.sleep(webSocketConfig.getReconnectInterval());
                
                // 启动监听器
                startListeners();
                
                // 记录完成时间
                long duration = System.currentTimeMillis() - startTime;
                log.info("所有WebSocket监听器已重启，耗时: {}毫秒", duration);
            } catch (Exception e) {
                log.error("重启WebSocket监听器异常", e);
                // 确保设置正确的运行状态
                running.set(false);
            }
        });
    }
    
    /**
     * 批量启动指定类型的WebSocket监听器
     *
     * @param type 监听器类型，可选值：depth、kline、trade
     * @return 是否成功启动
     */
    public boolean startListenersByType(String type) {
        log.info("启动{}类型的WebSocket监听器", type);
        
        try {
            switch (type.toLowerCase()) {
                case "depth":
                    if (marketDataConfig.isDepthEnabled()) {
                        depthDataListener.startDepthDataSubscription();
                    }
                    break;
                case "kline":
                    if (marketDataConfig.isKlineEnabled()) {
                        klineDataListener.startKlineDataSubscription();
                    }
                    break;
                case "trade":
                    if (marketDataConfig.isTradeEnabled()) {
                        tradeDataListener.startTradeDataSubscription();
                    }
                    break;
                default:
                    log.warn("未知的监听器类型: {}", type);
                    return false;
            }
            return true;
        } catch (Exception e) {
            log.error("启动{}类型的WebSocket监听器异常", type, e);
            return false;
        }
    }
    
    /**
     * 批量停止指定类型的WebSocket监听器
     *
     * @param type 监听器类型，可选值：depth、kline、trade
     * @return 是否成功停止
     */
    public boolean stopListenersByType(String type) {
        log.info("停止{}类型的WebSocket监听器", type);
        
        try {
            switch (type.toLowerCase()) {
                case "depth":
                    depthDataListener.stopDepthDataSubscription();
                    break;
                case "kline":
                    klineDataListener.stopKlineDataSubscription();
                    break;
                case "trade":
                    tradeDataListener.stopTradeDataSubscription();
                    break;
                default:
                    log.warn("未知的监听器类型: {}", type);
                    return false;
            }
            return true;
        } catch (Exception e) {
            log.error("停止{}类型的WebSocket监听器异常", type, e);
            return false;
        }
    }
    
    /**
     * 批量重启指定类型的WebSocket监听器
     *
     * @param type 监听器类型，可选值：depth、kline、trade
     * @return 是否成功重启
     */
    public boolean restartListenersByType(String type) {
        log.info("重启{}类型的WebSocket监听器", type);
        
        // 使用统一的虚拟线程执行器异步执行重启操作
        virtualThreadExecutor.submit(() -> {
            try {
                stopListenersByType(type);
                Thread.sleep(webSocketConfig.getReconnectInterval());
                startListenersByType(type);
                log.info("{}类型的WebSocket监听器已重启", type);
            } catch (Exception e) {
                log.error("重启{}类型的WebSocket监听器异常", type, e);
            }
        });
        
        return true;
    }
    
    /**
     * 注册WebSocket连接到健康检查器
     *
     * @param connectionId 连接ID
     * @param description  连接描述
     */
    public void registerConnection(int connectionId, String description) {
        connectionMap.put(description, connectionId);
        healthChecker.registerConnection(connectionId, description);
    }
    
    /**
     * 注销WebSocket连接
     *
     * @param description 连接描述
     */
    public void unregisterConnection(String description) {
        Integer connectionId = connectionMap.remove(description);
        if (connectionId != null) {
            healthChecker.unregisterConnection(connectionId);
        }
    }
    
    /**
     * 获取虚拟线程执行器
     * 
     * @return 虚拟线程执行器
     */
    public ExecutorService getVirtualThreadExecutor() {
        return virtualThreadExecutor;
    }
    
    /**
     * 检查监听器是否在运行
     * 
     * @return 监听器是否在运行
     */
    public boolean isRunning() {
        return running.get();
    }
    
    /**
     * 获取活跃连接数
     *
     * @return 活跃连接数
     */
    public int getActiveConnectionCount() {
        return connectionPool.getActiveConnectionCount();
    }
    
    /**
     * 获取总连接数
     *
     * @return 总连接数
     */
    public int getTotalConnectionCount() {
        return connectionPool.getTotalConnectionCount();
    }
    
    /**
     * 获取健康检查器
     *
     * @return 健康检查器
     */
    public WebSocketHealthChecker getHealthChecker() {
        return healthChecker;
    }
} 