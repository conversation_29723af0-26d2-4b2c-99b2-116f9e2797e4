package com.crypto.trading.common.monitor;

import com.crypto.trading.common.util.DatabaseExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * 数据完整性监控器
 * 监控虚拟线程数据处理的完整性，检测数据丢失情况
 */
@Component
public class DataIntegrityMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(DataIntegrityMonitor.class);
    
    /**
     * 监控统计
     */
    private final AtomicLong totalDataProcessed = new AtomicLong(0);
    private final AtomicLong dataLossDetected = new AtomicLong(0);
    private final AtomicLong tempFilesCreated = new AtomicLong(0);
    private final AtomicLong virtualThreadFailures = new AtomicLong(0);
    
    /**
     * 定时监控执行器
     */
    private ScheduledExecutorService monitorExecutor;
    
    /**
     * 临时文件目录
     */
    private final String tempDir = System.getProperty("java.io.tmpdir");
    
    @PostConstruct
    public void init() {
        log.info("初始化数据完整性监控器...");
        
        // 创建监控执行器
        monitorExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "data-integrity-monitor");
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动定期监控任务
        startMonitoringTasks();
        
        // 检查启动时的临时文件
        checkExistingTempFiles();
        
        log.info("数据完整性监控器已启动");
    }
    
    @PreDestroy
    public void destroy() {
        log.info("关闭数据完整性监控器...");
        
        if (monitorExecutor != null) {
            monitorExecutor.shutdown();
            try {
                if (!monitorExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 生成最终报告
        generateFinalReport();
        
        log.info("数据完整性监控器已关闭");
    }
    
    /**
     * 启动监控任务
     */
    private void startMonitoringTasks() {
        // 每分钟检查一次临时文件
        monitorExecutor.scheduleAtFixedRate(
                this::checkTempFiles,
                1, 1, TimeUnit.MINUTES);
        
        // 每5分钟生成一次监控报告
        monitorExecutor.scheduleAtFixedRate(
                this::generateMonitoringReport,
                5, 5, TimeUnit.MINUTES);
        
        // 每小时检查虚拟线程状态
        monitorExecutor.scheduleAtFixedRate(
                this::checkVirtualThreadStatus,
                1, 1, TimeUnit.HOURS);
    }
    
    /**
     * 检查临时文件
     */
    private void checkTempFiles() {
        try {
            Path tempPath = Paths.get(tempDir);
            if (!Files.exists(tempPath)) {
                return;
            }
            
            try (Stream<Path> files = Files.list(tempPath)) {
                long influxdbTempFiles = files
                        .filter(path -> path.getFileName().toString().startsWith("influxdb_"))
                        .count();
                
                if (influxdbTempFiles > 0) {
                    log.warn("检测到{}个InfluxDB临时文件，可能存在数据丢失", influxdbTempFiles);
                    dataLossDetected.addAndGet(influxdbTempFiles);
                    tempFilesCreated.set(influxdbTempFiles);
                }
            }
        } catch (IOException e) {
            log.error("检查临时文件时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查启动时存在的临时文件
     */
    private void checkExistingTempFiles() {
        try {
            Path tempPath = Paths.get(tempDir);
            if (!Files.exists(tempPath)) {
                return;
            }
            
            try (Stream<Path> files = Files.list(tempPath)) {
                long existingTempFiles = files
                        .filter(path -> {
                            String fileName = path.getFileName().toString();
                            return fileName.startsWith("influxdb_unsaved_data_") ||
                                   fileName.startsWith("influxdb_failed_");
                        })
                        .peek(path -> {
                            try {
                                long fileSize = Files.size(path);
                                log.warn("发现遗留的临时数据文件: {} (大小: {} bytes)", 
                                        path.getFileName(), fileSize);
                            } catch (IOException e) {
                                log.error("无法获取文件大小: {}", path.getFileName());
                            }
                        })
                        .count();
                
                if (existingTempFiles > 0) {
                    log.error("启动时发现{}个遗留的临时数据文件，表明上次关闭时可能发生了数据丢失", 
                            existingTempFiles);
                    dataLossDetected.addAndGet(existingTempFiles);
                    tempFilesCreated.set(existingTempFiles);
                }
            }
        } catch (IOException e) {
            log.error("检查遗留临时文件时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查虚拟线程状态
     */
    private void checkVirtualThreadStatus() {
        try {
            // 获取DatabaseExecutor统计信息
            var dbStats = DatabaseExecutor.getStatistics();
            long activeOperations = (Long) dbStats.get("activeOperations");
            long failedOperations = (Long) dbStats.get("failedOperations");
            double successRate = (Double) dbStats.get("successRate");
            
            log.info("虚拟线程状态检查 - 活跃操作: {}, 失败操作: {}, 成功率: {:.2f}%", 
                    activeOperations, failedOperations, successRate);
            
            // 检查是否有异常情况
            if (successRate < 95.0) {
                log.warn("数据库操作成功率较低: {:.2f}%，可能存在数据完整性风险", successRate);
                virtualThreadFailures.addAndGet(failedOperations);
            }
            
            if (activeOperations > 100) {
                log.warn("活跃数据库操作数量较高: {}，可能存在性能问题", activeOperations);
            }
            
        } catch (Exception e) {
            log.error("检查虚拟线程状态时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 生成监控报告
     */
    private void generateMonitoringReport() {
        try {
            var dbStats = DatabaseExecutor.getStatistics();
            
            log.info("=== 数据完整性监控报告 ===");
            log.info("报告时间: {}", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            log.info("总处理数据量: {}", totalDataProcessed.get());
            log.info("检测到的数据丢失次数: {}", dataLossDetected.get());
            log.info("创建的临时文件数: {}", tempFilesCreated.get());
            log.info("虚拟线程失败次数: {}", virtualThreadFailures.get());
            log.info("数据库操作统计: {}", dbStats);
            log.info("========================");
            
        } catch (Exception e) {
            log.error("生成监控报告时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 生成最终报告
     */
    private void generateFinalReport() {
        try {
            log.info("=== 数据完整性最终报告 ===");
            log.info("应用关闭时间: {}", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            log.info("总处理数据量: {}", totalDataProcessed.get());
            log.info("总数据丢失检测次数: {}", dataLossDetected.get());
            log.info("总临时文件创建数: {}", tempFilesCreated.get());
            log.info("总虚拟线程失败次数: {}", virtualThreadFailures.get());
            
            // 计算数据完整性评分
            double integrityScore = calculateIntegrityScore();
            log.info("数据完整性评分: {:.2f}/100", integrityScore);
            
            if (integrityScore < 90.0) {
                log.error("数据完整性评分较低，建议检查系统配置和虚拟线程使用方式");
            } else if (integrityScore < 95.0) {
                log.warn("数据完整性评分一般，建议优化虚拟线程处理逻辑");
            } else {
                log.info("数据完整性评分良好");
            }
            
            log.info("==========================");
            
        } catch (Exception e) {
            log.error("生成最终报告时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 计算数据完整性评分
     */
    private double calculateIntegrityScore() {
        long totalProcessed = totalDataProcessed.get();
        long dataLoss = dataLossDetected.get();
        long failures = virtualThreadFailures.get();
        
        if (totalProcessed == 0) {
            return 100.0; // 没有处理数据，认为是完整的
        }
        
        // 基础分数100分
        double score = 100.0;
        
        // 数据丢失扣分（每次丢失扣5分）
        score -= Math.min(dataLoss * 5, 50);
        
        // 虚拟线程失败扣分（每次失败扣1分）
        score -= Math.min(failures * 1, 30);
        
        // 临时文件创建扣分（每个文件扣2分）
        score -= Math.min(tempFilesCreated.get() * 2, 20);
        
        return Math.max(score, 0.0);
    }
    
    /**
     * 记录数据处理
     */
    public void recordDataProcessed(long count) {
        totalDataProcessed.addAndGet(count);
    }
    
    /**
     * 记录数据丢失
     */
    public void recordDataLoss(long count) {
        dataLossDetected.addAndGet(count);
        log.warn("记录数据丢失事件，丢失数量: {}", count);
    }
    
    /**
     * 记录临时文件创建
     */
    public void recordTempFileCreated() {
        tempFilesCreated.incrementAndGet();
        log.warn("记录临时文件创建事件");
    }
    
    /**
     * 记录虚拟线程失败
     */
    public void recordVirtualThreadFailure() {
        virtualThreadFailures.incrementAndGet();
        log.warn("记录虚拟线程失败事件");
    }
    
    /**
     * 获取监控统计信息
     */
    public MonitoringStats getStats() {
        return new MonitoringStats(
                totalDataProcessed.get(),
                dataLossDetected.get(),
                tempFilesCreated.get(),
                virtualThreadFailures.get(),
                calculateIntegrityScore()
        );
    }
    
    /**
     * 监控统计信息记录
     */
    public static class MonitoringStats {
        private final long totalDataProcessed;
        private final long dataLossDetected;
        private final long tempFilesCreated;
        private final long virtualThreadFailures;
        private final double integrityScore;
        
        public MonitoringStats(long totalDataProcessed, long dataLossDetected, 
                             long tempFilesCreated, long virtualThreadFailures, 
                             double integrityScore) {
            this.totalDataProcessed = totalDataProcessed;
            this.dataLossDetected = dataLossDetected;
            this.tempFilesCreated = tempFilesCreated;
            this.virtualThreadFailures = virtualThreadFailures;
            this.integrityScore = integrityScore;
        }
        
        // Getters
        public long getTotalDataProcessed() { return totalDataProcessed; }
        public long getDataLossDetected() { return dataLossDetected; }
        public long getTempFilesCreated() { return tempFilesCreated; }
        public long getVirtualThreadFailures() { return virtualThreadFailures; }
        public double getIntegrityScore() { return integrityScore; }
        
        @Override
        public String toString() {
            return String.format("MonitoringStats{processed=%d, lost=%d, tempFiles=%d, failures=%d, score=%.2f}", 
                    totalDataProcessed, dataLossDetected, tempFilesCreated, virtualThreadFailures, integrityScore);
        }
    }
}
