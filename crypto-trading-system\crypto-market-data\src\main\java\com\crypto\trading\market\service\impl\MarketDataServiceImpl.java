package com.crypto.trading.market.service.impl;

import com.crypto.trading.market.manager.WebSocketManager;
import com.crypto.trading.market.service.MarketDataService;
import com.crypto.trading.market.config.MarketDataConfig;
import com.crypto.trading.market.listener.DepthDataListener;
import com.crypto.trading.market.listener.KlineDataListener;
import com.crypto.trading.market.listener.TradeDataListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * 市场数据服务实现类
 * 负责启动和管理市场数据的WebSocket连接
 *
 * <AUTHOR>
 * @create 2024-07-26
 */
@Service
public class MarketDataServiceImpl implements MarketDataService {

    private static final Logger log = LoggerFactory.getLogger(MarketDataServiceImpl.class);

    @Autowired
    private WebSocketManager webSocketManager;
    
    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private KlineDataListener klineDataListener;

    @Autowired
    private DepthDataListener depthDataListener;

    @Autowired
    private TradeDataListener tradeDataListener;

    /**
     * 启动市场数据服务
     * 主要是启动WebSocket连接，订阅市场数据
     */
    @Override
    @PostConstruct
    public void start() {
        log.info("启动市场数据服务...");
        try {
            // 统一使用WebSocketManager启动所有监听器
            // 这样可以避免重复启动和虚拟线程管理混乱的问题
            log.info("通过WebSocketManager启动所有监听器...");
            webSocketManager.startListeners();

            // 验证启动状态
            if (webSocketManager.isRunning()) {
                log.info("市场数据服务启动成功，WebSocket监听器运行状态: {}", webSocketManager.isRunning());
            } else {
                log.error("市场数据服务启动失败，WebSocket监听器未正常运行");
                throw new RuntimeException("WebSocket监听器启动失败");
            }
        } catch (Exception e) {
            log.error("启动市场数据服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("启动市场数据服务失败", e);
        }
    }

    /**
     * 停止市场数据服务
     * 关闭WebSocket连接
     */
    @Override
    @PreDestroy
    public void stop() {
        log.info("停止市场数据服务...");
        try {
            // 统一使用WebSocketManager停止所有监听器
            log.info("通过WebSocketManager停止所有监听器...");
            webSocketManager.stopListeners();

            // 验证停止状态
            if (!webSocketManager.isRunning()) {
                log.info("市场数据服务停止成功，WebSocket监听器运行状态: {}", webSocketManager.isRunning());
            } else {
                log.warn("市场数据服务停止可能不完整，WebSocket监听器仍在运行");
            }
        } catch (Exception e) {
            log.error("停止市场数据服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("停止市场数据服务失败", e);
        }
    }

    /**
     * 重启市场数据服务
     * 先停止，然后再启动
     */
    @Override
    public void restart() {
        log.info("重启市场数据服务...");
        try {
            stop();
            // 等待一段时间确保连接完全关闭
            Thread.sleep(1000);
            start();
            log.info("市场数据服务重启成功");
        } catch (Exception e) {
            log.error("重启市场数据服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("重启市场数据服务失败", e);
        }
    }

    /**
     * 获取服务状态
     *
     * @return 服务是否运行中
     */
    @Override
    public boolean isRunning() {
        return webSocketManager.isRunning();
    }
    
    /**
     * 获取市场数据配置
     *
     * @return 市场数据配置信息
     */
    public MarketDataConfig getMarketDataConfig() {
        return marketDataConfig;
    }
}