package com.crypto.trading.common.util;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 数据库执行器工具类
 * <p>
 * 提供使用虚拟线程执行数据库操作的工具方法，优化数据库I/O操作性能。
 * 支持事务、批处理、MyBatis-Plus集成等功能。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DatabaseExecutor {

    private static final Logger log = LoggerFactory.getLogger(DatabaseExecutor.class);

    /**
     * 虚拟线程执行器
     */
    private static final ExecutorService VIRTUAL_THREAD_EXECUTOR = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 关闭标志
     */
    private static volatile boolean isShuttingDown = false;

    /**
     * 正在执行的事务计数器
     */
    private static final AtomicLong ACTIVE_TRANSACTIONS = new AtomicLong(0);
    
    /**
     * 操作统计 - 总执行次数
     */
    private static final AtomicLong TOTAL_EXECUTIONS = new AtomicLong(0);
    
    /**
     * 操作统计 - 总执行时间（毫秒）
     */
    private static final AtomicLong TOTAL_EXECUTION_TIME = new AtomicLong(0);
    
    /**
     * 操作统计 - 当前活跃操作数
     */
    private static final AtomicInteger ACTIVE_OPERATIONS = new AtomicInteger(0);
    
    /**
     * 操作统计 - 失败操作数
     */
    private static final AtomicLong FAILED_OPERATIONS = new AtomicLong(0);

    /**
     * 私有构造函数，防止实例化
     */
    private DatabaseExecutor() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 使用虚拟线程异步执行数据库操作
     *
     * @param task 数据库操作任务
     * @param <T>  返回值类型
     * @return 包含操作结果的CompletableFuture
     */
    public static <T> CompletableFuture<T> executeAsync(Callable<T> task) {
        ACTIVE_OPERATIONS.incrementAndGet();
        long startTime = System.currentTimeMillis();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                T result = task.call();
                recordSuccess(startTime);
                return result;
            } catch (Exception e) {
                recordFailure(startTime);
                throw new RuntimeException("数据库操作异常", e);
            } finally {
                ACTIVE_OPERATIONS.decrementAndGet();
            }
        }, VIRTUAL_THREAD_EXECUTOR);
    }

    /**
     * 使用虚拟线程异步执行无返回值的数据库操作
     *
     * @param task 数据库操作任务
     * @return 操作完成的CompletableFuture
     */
    public static CompletableFuture<Void> executeAsync(Runnable task) {
        ACTIVE_OPERATIONS.incrementAndGet();
        long startTime = System.currentTimeMillis();
        
        return CompletableFuture.runAsync(() -> {
            try {
                task.run();
                recordSuccess(startTime);
            } catch (Exception e) {
                recordFailure(startTime);
                throw new RuntimeException("数据库操作异常", e);
            } finally {
                ACTIVE_OPERATIONS.decrementAndGet();
            }
        }, VIRTUAL_THREAD_EXECUTOR);
    }

    /**
     * 在事务中使用虚拟线程异步执行数据库操作
     *
     * @param transactionTemplate 事务模板
     * @param task                数据库操作任务
     * @param <T>                 返回值类型
     * @return 包含操作结果的CompletableFuture
     */
    public static <T> CompletableFuture<T> executeInTransactionAsync(
            TransactionTemplate transactionTemplate,
            Supplier<T> task) {
        // 检查是否正在关闭
        if (isShuttingDown) {
            log.warn("系统正在关闭，拒绝新的事务操作");
            return CompletableFuture.failedFuture(
                    new IllegalStateException("系统正在关闭，无法执行新的事务操作"));
        }

        ACTIVE_OPERATIONS.incrementAndGet();
        ACTIVE_TRANSACTIONS.incrementAndGet();
        long startTime = System.currentTimeMillis();

        return CompletableFuture.supplyAsync(() ->
                transactionTemplate.execute(status -> {
                    try {
                        T result = task.get();
                        recordSuccess(startTime);
                        return result;
                    } catch (Exception e) {
                        log.error("事务内数据库操作异常", e);
                        status.setRollbackOnly();
                        recordFailure(startTime);
                        throw new RuntimeException("事务内数据库操作异常", e);
                    } finally {
                        ACTIVE_OPERATIONS.decrementAndGet();
                        ACTIVE_TRANSACTIONS.decrementAndGet();
                    }
                }), VIRTUAL_THREAD_EXECUTOR);
    }

    /**
     * 在事务中使用虚拟线程异步执行无返回值的数据库操作
     *
     * @param transactionTemplate 事务模板
     * @param task                数据库操作任务
     * @return 操作完成的CompletableFuture
     */
    public static CompletableFuture<Void> executeInTransactionAsync(
            TransactionTemplate transactionTemplate,
            Runnable task) {
        // 检查是否正在关闭
        if (isShuttingDown) {
            log.warn("系统正在关闭，拒绝新的事务操作");
            return CompletableFuture.failedFuture(
                    new IllegalStateException("系统正在关闭，无法执行新的事务操作"));
        }

        ACTIVE_OPERATIONS.incrementAndGet();
        ACTIVE_TRANSACTIONS.incrementAndGet();
        long startTime = System.currentTimeMillis();

        return CompletableFuture.supplyAsync(() ->
                transactionTemplate.execute(status -> {
                    try {
                        task.run();
                        recordSuccess(startTime);
                        return null;
                    } catch (Exception e) {
                        log.error("事务内数据库操作异常", e);
                        status.setRollbackOnly();
                        recordFailure(startTime);
                        throw new RuntimeException("事务内数据库操作异常", e);
                    } finally {
                        ACTIVE_OPERATIONS.decrementAndGet();
                        ACTIVE_TRANSACTIONS.decrementAndGet();
                    }
                }), VIRTUAL_THREAD_EXECUTOR);
    }

    /**
     * 批量执行数据库操作，并等待所有操作完成
     *
     * @param tasks 数据库操作任务列表
     * @param <T>   返回值类型
     * @return 包含所有操作结果的CompletableFuture
     */
    @SafeVarargs
    public static <T> CompletableFuture<Void> executeAllAsync(Callable<T>... tasks) {
        CompletableFuture<?>[] futures = new CompletableFuture[tasks.length];
        for (int i = 0; i < tasks.length; i++) {
            final Callable<T> task = tasks[i];
            futures[i] = executeAsync(task);
        }
        return CompletableFuture.allOf(futures);
    }

    /**
     * 批量执行无返回值的数据库操作，并等待所有操作完成
     *
     * @param tasks 数据库操作任务列表
     * @return 包含所有操作结果的CompletableFuture
     */
    public static CompletableFuture<Void> executeAllAsync(Runnable... tasks) {
        CompletableFuture<?>[] futures = new CompletableFuture[tasks.length];
        for (int i = 0; i < tasks.length; i++) {
            final Runnable task = tasks[i];
            futures[i] = executeAsync(task);
        }
        return CompletableFuture.allOf(futures);
    }
    
    /**
     * 使用JdbcTemplate异步执行查询
     *
     * @param jdbcTemplate JdbcTemplate实例
     * @param sql SQL查询语句
     * @param rowMapper 行映射器
     * @param args 查询参数
     * @param <T> 返回值类型
     * @return 包含查询结果的CompletableFuture
     */
    public static <T> CompletableFuture<List<T>> queryAsync(
            JdbcTemplate jdbcTemplate,
            String sql,
            RowMapper<T> rowMapper,
            Object... args) {
        return executeAsync(() -> jdbcTemplate.query(sql, rowMapper, args));
    }
    
    /**
     * 使用JdbcTemplate异步执行更新
     *
     * @param jdbcTemplate JdbcTemplate实例
     * @param sql SQL更新语句
     * @param args 更新参数
     * @return 包含影响行数的CompletableFuture
     */
    public static CompletableFuture<Integer> updateAsync(
            JdbcTemplate jdbcTemplate,
            String sql,
            Object... args) {
        return executeAsync(() -> jdbcTemplate.update(sql, args));
    }
    
    /**
     * 使用JdbcTemplate异步执行批量更新
     *
     * @param jdbcTemplate JdbcTemplate实例
     * @param sql SQL更新语句
     * @param batchArgs 批量参数
     * @return 包含每批影响行数的CompletableFuture
     */
    public static CompletableFuture<int[]> batchUpdateAsync(
            JdbcTemplate jdbcTemplate,
            String sql,
            List<Object[]> batchArgs) {
        return executeAsync(() -> jdbcTemplate.batchUpdate(sql, batchArgs));
    }
    
    /**
     * 使用MyBatis-Plus的BaseMapper异步查询实体
     *
     * @param baseMapper BaseMapper实例
     * @param id 实体ID
     * @param <T> 实体类型
     * @return 包含实体的CompletableFuture
     */
    public static <T> CompletableFuture<T> selectByIdAsync(BaseMapper<T> baseMapper, Serializable id) {
        return executeAsync(() -> baseMapper.selectById(id));
    }
    
    /**
     * 使用MyBatis-Plus的BaseMapper异步查询实体列表
     *
     * @param baseMapper BaseMapper实例
     * @param queryWrapper 查询条件
     * @param <T> 实体类型
     * @return 包含实体列表的CompletableFuture
     */
    public static <T> CompletableFuture<List<T>> selectListAsync(
            BaseMapper<T> baseMapper,
            Wrapper<T> queryWrapper) {
        return executeAsync(() -> baseMapper.selectList(queryWrapper));
    }
    
    /**
     * 使用MyBatis-Plus的BaseMapper异步分页查询
     *
     * @param baseMapper BaseMapper实例
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @param <T> 实体类型
     * @return 包含分页结果的CompletableFuture
     */
    public static <T> CompletableFuture<IPage<T>> selectPageAsync(
            BaseMapper<T> baseMapper,
            Page<T> page,
            Wrapper<T> queryWrapper) {
        return executeAsync(() -> baseMapper.selectPage(page, queryWrapper));
    }
    
    /**
     * 使用MyBatis-Plus的BaseMapper异步插入实体
     *
     * @param baseMapper BaseMapper实例
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 包含影响行数的CompletableFuture
     */
    public static <T> CompletableFuture<Integer> insertAsync(BaseMapper<T> baseMapper, T entity) {
        return executeAsync(() -> baseMapper.insert(entity));
    }
    
    /**
     * 使用MyBatis-Plus的BaseMapper异步更新实体
     *
     * @param baseMapper BaseMapper实例
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 包含影响行数的CompletableFuture
     */
    public static <T> CompletableFuture<Integer> updateByIdAsync(BaseMapper<T> baseMapper, T entity) {
        return executeAsync(() -> baseMapper.updateById(entity));
    }
    
    /**
     * 使用MyBatis-Plus的BaseMapper异步删除实体
     *
     * @param baseMapper BaseMapper实例
     * @param id 实体ID
     * @param <T> 实体类型
     * @return 包含影响行数的CompletableFuture
     */
    public static <T> CompletableFuture<Integer> deleteByIdAsync(BaseMapper<T> baseMapper, Serializable id) {
        return executeAsync(() -> baseMapper.deleteById(id));
    }
    
    /**
     * 使用MyBatis-Plus的IService异步保存实体
     *
     * @param service IService实例
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 包含保存结果的CompletableFuture
     */
    public static <T> CompletableFuture<Boolean> saveAsync(IService<T> service, T entity) {
        return executeAsync(() -> service.save(entity));
    }
    
    /**
     * 使用MyBatis-Plus的IService异步批量保存实体
     *
     * @param service IService实例
     * @param entityList 实体列表
     * @param <T> 实体类型
     * @return 包含保存结果的CompletableFuture
     */
    public static <T> CompletableFuture<Boolean> saveBatchAsync(
            IService<T> service,
            Collection<T> entityList) {
        return executeAsync(() -> service.saveBatch(entityList));
    }
    
    /**
     * 使用MyBatis-Plus的IService异步批量保存或更新实体
     *
     * @param service IService实例
     * @param entityList 实体列表
     * @param <T> 实体类型
     * @return 包含保存结果的CompletableFuture
     */
    public static <T> CompletableFuture<Boolean> saveOrUpdateBatchAsync(
            IService<T> service,
            Collection<T> entityList) {
        return executeAsync(() -> service.saveOrUpdateBatch(entityList));
    }
    
    /**
     * 批量处理集合数据，每批异步执行数据库操作
     *
     * @param collection 数据集合
     * @param batchSize 每批大小
     * @param batchProcessor 批处理器
     * @param <T> 数据类型
     * @return 包含所有批处理结果的CompletableFuture
     */
    public static <T> CompletableFuture<Void> processBatchAsync(
            Collection<T> collection,
            int batchSize,
            Consumer<List<T>> batchProcessor) {
        if (collection == null || collection.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }
        
        // 将集合分批
        List<List<T>> batches = collection.stream()
                .collect(Collectors.groupingBy(item -> {
                    int index = collection.stream().toList().indexOf(item);
                    return index / batchSize;
                }))
                .values()
                .stream()
                .toList();
        
        // 异步处理每批数据
        List<CompletableFuture<Void>> futures = batches.stream()
                .map(batch -> executeAsync(() -> batchProcessor.accept(batch)))
                .toList();
        
        // 等待所有批处理完成
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }
    
    /**
     * 批量处理集合数据，每批异步执行数据库操作，并返回处理结果
     *
     * @param collection 数据集合
     * @param batchSize 每批大小
     * @param batchProcessor 批处理器
     * @param <T> 数据类型
     * @param <R> 结果类型
     * @return 包含所有批处理结果的CompletableFuture
     */
    public static <T, R> CompletableFuture<List<R>> processBatchWithResultAsync(
            Collection<T> collection,
            int batchSize,
            Function<List<T>, List<R>> batchProcessor) {
        if (collection == null || collection.isEmpty()) {
            return CompletableFuture.completedFuture(List.of());
        }
        
        // 将集合分批
        List<List<T>> batches = collection.stream()
                .collect(Collectors.groupingBy(item -> {
                    int index = collection.stream().toList().indexOf(item);
                    return index / batchSize;
                }))
                .values()
                .stream()
                .toList();
        
        // 异步处理每批数据
        List<CompletableFuture<List<R>>> futures = batches.stream()
                .map(batch -> executeAsync(() -> batchProcessor.apply(batch)))
                .toList();
        
        // 等待所有批处理完成并合并结果
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .collect(Collectors.toList()));
    }
    
    /**
     * 记录操作成功
     *
     * @param startTime 操作开始时间
     */
    private static void recordSuccess(long startTime) {
        TOTAL_EXECUTIONS.incrementAndGet();
        TOTAL_EXECUTION_TIME.addAndGet(System.currentTimeMillis() - startTime);
    }
    
    /**
     * 记录操作失败
     *
     * @param startTime 操作开始时间
     */
    private static void recordFailure(long startTime) {
        TOTAL_EXECUTIONS.incrementAndGet();
        TOTAL_EXECUTION_TIME.addAndGet(System.currentTimeMillis() - startTime);
        FAILED_OPERATIONS.incrementAndGet();
    }
    
    /**
     * 获取数据库操作统计信息
     *
     * @return 统计信息Map
     */
    public static Map<String, Object> getStatistics() {
        long totalExecutions = TOTAL_EXECUTIONS.get();
        long totalExecutionTime = TOTAL_EXECUTION_TIME.get();
        int activeOperations = ACTIVE_OPERATIONS.get();
        long failedOperations = FAILED_OPERATIONS.get();
        
        double avgExecutionTime = totalExecutions > 0 ? 
                (double) totalExecutionTime / totalExecutions : 0;
        double successRate = totalExecutions > 0 ? 
                100.0 * (totalExecutions - failedOperations) / totalExecutions : 0;
        
        return Map.of(
                "totalExecutions", totalExecutions,
                "totalExecutionTime", totalExecutionTime,
                "activeOperations", activeOperations,
                "failedOperations", failedOperations,
                "avgExecutionTime", avgExecutionTime,
                "successRate", successRate
        );
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStatistics() {
        TOTAL_EXECUTIONS.set(0);
        TOTAL_EXECUTION_TIME.set(0);
        FAILED_OPERATIONS.set(0);
        // 不重置ACTIVE_OPERATIONS，因为可能有正在进行的操作
    }

    /**
     * 关闭虚拟线程执行器
     * 等待所有活跃的数据库操作和事务完成
     */
    public static void shutdown() {
        log.info("开始关闭DatabaseExecutor...");
        isShuttingDown = true;

        // 等待所有活跃的事务完成
        waitForActiveTransactions();

        // 关闭虚拟线程执行器
        VIRTUAL_THREAD_EXECUTOR.shutdown();
        try {
            if (!VIRTUAL_THREAD_EXECUTOR.awaitTermination(60, TimeUnit.SECONDS)) {
                log.warn("虚拟线程执行器未能在60秒内完成，强制关闭");
                VIRTUAL_THREAD_EXECUTOR.shutdownNow();

                // 再等待30秒
                if (!VIRTUAL_THREAD_EXECUTOR.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.error("虚拟线程执行器强制关闭后仍未完成");
                }
            }
        } catch (InterruptedException e) {
            log.warn("等待虚拟线程执行器关闭时被中断");
            VIRTUAL_THREAD_EXECUTOR.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("DatabaseExecutor已关闭，活跃操作数: {}, 活跃事务数: {}",
                ACTIVE_OPERATIONS.get(), ACTIVE_TRANSACTIONS.get());
    }

    /**
     * 等待所有活跃的事务完成
     */
    private static void waitForActiveTransactions() {
        int maxWaitSeconds = 120; // 最多等待2分钟
        int waitedSeconds = 0;

        while (ACTIVE_TRANSACTIONS.get() > 0 && waitedSeconds < maxWaitSeconds) {
            try {
                Thread.sleep(1000);
                waitedSeconds++;

                if (waitedSeconds % 10 == 0) {
                    log.info("等待{}个活跃事务完成，已等待{}秒...",
                            ACTIVE_TRANSACTIONS.get(), waitedSeconds);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待活跃事务完成时被中断");
                break;
            }
        }

        if (ACTIVE_TRANSACTIONS.get() > 0) {
            log.warn("等待活跃事务完成超时，仍有{}个事务未完成", ACTIVE_TRANSACTIONS.get());
        } else {
            log.info("所有活跃事务已完成，等待时间: {}秒", waitedSeconds);
        }
    }

    /**
     * 获取当前活跃事务数量
     */
    public static long getActiveTransactionCount() {
        return ACTIVE_TRANSACTIONS.get();
    }

    /**
     * 检查是否正在关闭
     */
    public static boolean isShuttingDown() {
        return isShuttingDown;
    }
}